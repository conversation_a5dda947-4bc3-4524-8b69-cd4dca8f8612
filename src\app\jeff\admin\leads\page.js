'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Container,
  Paper,
  Snackbar,
  Alert,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import JeffAdminPanel from 'views/Jeff/JeffAdminPanel';
import { useUser } from 'views/Jeff/Utils/getUser';
import { useRouter } from 'next/navigation';
import SearchIcon from '@mui/icons-material/Search';
import RefreshIcon from '@mui/icons-material/Refresh';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import BusinessIcon from '@mui/icons-material/Business';

// Import our custom components
import TabPanel from './components/TabPanel';
import JobsTable from './components/JobsTable';
import SingleLeadForm from './components/SingleLeadForm';
import BatchUploadForm from './components/BatchUploadForm';
import DeleteConfirmationDialog from './components/DeleteConfirmationDialog';
import LeadScraperTabs from './components/LeadScraperTabs';
import LeadModifierTabs from './components/LeadModifierTabs';
import ExportLinksResultDialog from './components/ExportLinksResultDialog';

// Import utils
import { 
  fetchLeadJobs, 
  deleteLeadJob, 
  exportLeads, 
  submitSingleLead, 
  uploadCompanyData, 
  uploadLeadGenerationFile,
  uploadLeadTypeData,
  exportLeadInput,
  checkLeadJobStatus,
  exportLeadLinks
} from './utils/api';
import { 
  initialSingleLeadForm, 
  handleSingleLeadInputChange,
  handleAddUrl,
  handleRemoveUrl,
  handleUrlKeyPress
} from './utils/formHandlers';

const LeadManagementPage = () => {
  const { data: user, isLoading: isUserLoading } = useUser();
  const router = useRouter();
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';
  
  // Define theme based on dark mode
  const theme = createTheme({
    palette: {
      mode: isDarkMode ? 'dark' : 'light',
      primary: {
        main: isDarkMode ? '#90CAF9' : '#1976d2',
      },
      background: {
        default: isDarkMode ? '#1A202C' : '#F8FAFC',
        paper: isDarkMode ? '#2D3748' : '#FFFFFF',
      },
      text: {
        primary: isDarkMode ? '#E2E8F0' : '#4A5568',
        secondary: isDarkMode ? '#A0AEC0' : '#718096',
      },
    },
  });
  
  // State for jobs list and operations
  const [jobs, setJobs] = useState([]);
  const [isJobsLoading, setIsJobsLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedJobs, setSelectedJobs] = useState([]);
  
  // Pagination state
  const [page, setPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [pagination, setPagination] = useState(null);

  // Tab state
  const [tabValue, setTabValue] = useState(0);

  // Form states
  const [singleLeadForm, setSingleLeadForm] = useState(initialSingleLeadForm);
  const [currentUrl, setCurrentUrl] = useState('');
  
  // Upload states
  const [uploadFile, setUploadFile] = useState(null);
  const [uploadType, setUploadType] = useState('company');
  const [uploadOperation, setUploadOperation] = useState('insert');
  const [skipErrorRows, setSkipErrorRows] = useState(false);
  const [allowBlankRows, setAllowBlankRows] = useState(false);
  
  // Dialog states
  const [openUploadDialog, setOpenUploadDialog] = useState(false);
  const [openSingleLeadDialog, setOpenSingleLeadDialog] = useState(false);

  // Batch processing states
  const [csvFile, setCsvFile] = useState(null);
  const [batchMode, setBatchMode] = useState('serp');
  const [batchUseDomain, setBatchUseDomain] = useState('0');
  
  // Delete confirmation dialog
  const [deleteConfirmDialog, setDeleteConfirmDialog] = useState({
    open: false,
    jobId: null
  });

  // Export links result dialog
  const [exportLinksDialog, setExportLinksDialog] = useState({
    open: false,
    data: null,
    jobId: null,
    includeDebug: false
  });

  // Notifications
  const [snackBar, setSnackBar] = useState({
    isOpen: false,
    message: '',
    severity: 'success',
  });

  useEffect(() => {
    if (!isUserLoading && !user) {
      router.push('/jeff/login');
    } else if (!isUserLoading && user && user?.userType && user?.userType !== 'admin') {
      router.push('/jeff/dashboard');
    } else if (!isUserLoading && user && user?.userType && user?.userType === 'admin') {
      // Initialize by fetching lead jobs when component mounts
      loadJobs();
    }
  }, [isUserLoading, user, router]);

  const loadJobs = async () => {
    setIsJobsLoading(true);
    setSelectedJobs([]);
    
    try {
      const result = await fetchLeadJobs(page, rowsPerPage);
      
      setJobs(result.data);
      setPagination(result.pagination);
      setSnackBar({
        isOpen: true,
        message: result.message,
        severity: result.success ? 'success' : 'warning',
      });
    } catch (error) {
      console.error('Error in loadJobs:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to load jobs: ' + error.message,
        severity: 'error',
      });
    } finally {
      setIsJobsLoading(false);
    }
  };

  const handleDeleteJob = async (jobId) => {
    setActionLoading(jobId);
    try {
      const result = await deleteLeadJob(jobId);
      
      setSnackBar({
        isOpen: true,
        message: result.message,
        severity: result.success ? 'success' : 'error',
      });
      
      if (result.success) {
        // Remove the job from the state
        setJobs(jobs.filter(job => job.id !== jobId));
      }
    } catch (error) {
      console.error('Error deleting job:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to delete job: ' + error.message,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleExportJob = async (jobId) => {
    setActionLoading(jobId);
    try {
      const result = await exportLeads(jobId);
      
      if (result.success && result.url) {
        // Create a direct download link
        const link = document.createElement('a');
        link.href = result.url;
        link.setAttribute('download', `lead-export-${jobId}.zip`);
        link.click();
        
        setSnackBar({
          isOpen: true,
          message: 'Export download started',
          severity: 'success',
        });
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message || 'Failed to generate export URL',
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error exporting job:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to export job: ' + error.message,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleFileChange = (event) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
    }
  };

  const handleJobSelection = (jobId) => {
    const selectedIndex = selectedJobs.indexOf(jobId);
    let newSelected = [];
    
    if (selectedIndex === -1) {
      newSelected = [...selectedJobs, jobId];
    } else {
      newSelected = selectedJobs.filter(id => id !== jobId);
    }
    
    setSelectedJobs(newSelected);
  };

  const handleSelectAllJobs = (event) => {
    if (event.target.checked) {
      const newSelected = jobs.map(job => job.id);
      setSelectedJobs(newSelected);
      return;
    }
    setSelectedJobs([]);
  };

  const handleBulkExport = async () => {
    if (selectedJobs.length === 0) {
      setSnackBar({
        isOpen: true,
        message: 'Please select at least one job to export',
        severity: 'warning',
      });
      return;
    }
    
    setActionLoading('bulk');
    try {
      const result = await exportLeads(selectedJobs);
      
      if (result.success && result.url) {
        // Create a direct download link
        const link = document.createElement('a');
        link.href = result.url;
        link.setAttribute('download', 'lead-export-bulk.zip');
        link.click();
        
        setSnackBar({
          isOpen: true,
          message: 'Bulk export download started',
          severity: 'success',
        });
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message || 'Failed to generate bulk export URL',
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error with bulk export:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to perform bulk export: ' + error.message,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleCloseDialog = () => {
    setOpenUploadDialog(false);
  };

  const handleSingleLeadDialogClose = () => {
    setOpenSingleLeadDialog(false);
    setSingleLeadForm(initialSingleLeadForm);
    setCurrentUrl('');
  };

  const handleLocalSingleLeadInputChange = (e) => {
    handleSingleLeadInputChange(e, setSingleLeadForm);
  };

  const handleSingleLeadSubmit = async () => {
    if (!singleLeadForm.sellerName.trim()) {
      setSnackBar({
        isOpen: true,
        message: 'Seller name is required',
        severity: 'warning',
      });
      return;
    }

    setActionLoading('singleLead');
    try {
      // Format data for single lead API according to requirements
      const formData = {
        // Required parameters
        sellerName: singleLeadForm.sellerName.trim(),
        businessName: singleLeadForm.businessName.trim() || singleLeadForm.sellerName.trim(),
        address: singleLeadForm.address.trim() || '',
        useDomain: singleLeadForm.useDomain || '0',
        // Optional parameters
        industry: singleLeadForm.industry?.trim() || '',
        email: singleLeadForm.email?.trim() || '',
        sellerUrl: singleLeadForm.urls && singleLeadForm.urls.length > 0 ? singleLeadForm.urls[0] : '',
        // Include all URLs if any
        urls: singleLeadForm.urls && singleLeadForm.urls.length > 0 ? singleLeadForm.urls : undefined,
        // Adding country as a default
        country: 'US'
      };
      
      const result = await submitSingleLead(formData);
      
      if (result.success) {
        setSnackBar({
          isOpen: true,
          message: result.message || 'Lead generation job created successfully',
          severity: 'success',
        });
        
        // Reset form state
        setSingleLeadForm(initialSingleLeadForm);
        setCurrentUrl('');
        
        // Reload jobs list
        loadJobs();
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message,
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error submitting single lead:', error);
      setSnackBar({
        isOpen: true,
        message: `Failed to generate lead: ${error.message}`,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleLocalAddUrl = () => {
    handleAddUrl(currentUrl, singleLeadForm, setSingleLeadForm, setCurrentUrl);
  };

  const handleLocalRemoveUrl = (index) => {
    handleRemoveUrl(index, singleLeadForm, setSingleLeadForm);
  };

  const handleLocalUrlKeyPress = (e) => {
    handleUrlKeyPress(e, currentUrl, singleLeadForm, setSingleLeadForm, setCurrentUrl);
  };

  const handleFileUpload = async () => {
    if (!uploadFile) return;

    setActionLoading('fileUpload');
    try {
      let result;

      if (uploadType === 'company') {
        // Use company data API
        result = await uploadCompanyData(
          uploadFile, 
          uploadOperation, 
          uploadType,
          skipErrorRows,
          allowBlankRows
        );
      } else if (uploadType === 'prospect' || uploadType === 'matching') {
        // Use lead type API for prospect or matching
        result = await uploadLeadTypeData(uploadFile, uploadType);
      } else {
        throw new Error(`Unknown upload type: ${uploadType}`);
      }
      
      if (result.success) {
        // Reset form state on success
        setUploadFile(null);
        
        setSnackBar({
          isOpen: true,
          message: result.message,
          severity: 'success',
        });
        
        // Reload jobs list to show the new job
        loadJobs();
      } else if (result.errorData) {
        // Handle case where API returns error data CSV
        const blob = new Blob([result.errorData], { type: 'text/csv' });
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blob);
        link.download = `error-${uploadFile.name}`;
        link.click();
        
        setSnackBar({
          isOpen: true,
          message: result.message,
          severity: 'warning',
        });
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message,
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      setSnackBar({
        isOpen: true,
        message: `Upload failed: ${error.message}`,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  const handleBatchLeadUpload = async () => {
    if (!csvFile) {
      setSnackBar({
        isOpen: true,
        message: 'Please select a CSV file first',
        severity: 'warning',
      });
      return;
    }

    setActionLoading('batchUpload');
    try {
      const result = await uploadLeadGenerationFile(
        csvFile,
        batchUseDomain,
        batchMode
      );
      
      if (result.success) {
        setSnackBar({
          isOpen: true,
          message: result.message,
          severity: 'success',
        });
        
        // Reset file state
        setCsvFile(null);
        
        // Reload jobs list
        loadJobs();
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message,
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error uploading lead generation file:', error);
      setSnackBar({
        isOpen: true,
        message: `Failed to generate leads: ${error.message}`,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const openDeleteConfirmation = (jobId) => {
    setDeleteConfirmDialog({
      open: true,
      jobId
    });
  };

  const closeDeleteConfirmation = () => {
    setDeleteConfirmDialog({
      open: false,
      jobId: null
    });
  };

  const confirmAndDeleteJob = () => {
    if (deleteConfirmDialog.jobId) {
      handleDeleteJob(deleteConfirmDialog.jobId);
      closeDeleteConfirmation();
    }
  };


  // Add function to export input data
  const handleExportInputData = async (jobId) => {
    setActionLoading(jobId);
    try {
      const result = await exportLeadInput(jobId);
      
      if (result.success && result.url) {
        // Create a direct download link
        const link = document.createElement('a');
        link.href = result.url;
        link.setAttribute('download', `lead-input-${jobId}.csv`);
        link.click();
        
        setSnackBar({
          isOpen: true,
          message: 'Input data export download started',
          severity: 'success',
        });
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message || 'Failed to generate input data export URL',
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error exporting input data:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to export input data: ' + error.message,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };
  
  // Add function to check job status
  const handleCheckJobStatus = async (jobId) => {
    setActionLoading(`status-${jobId}`);
    try {
      await checkLeadJobStatus(jobId);
      await loadJobs();
      setSnackBar({
        open: true,
        message: 'Job status updated successfully',
        severity: 'success'
      });
    } catch (error) {
      setSnackBar({
        open: true,
        message: error.message || 'Failed to check job status',
        severity: 'error'
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleViewJobDetails = async (jobId) => {
    setActionLoading(`details-${jobId}`);
    try {
      await checkLeadJobStatus(jobId);
      await loadJobs();
    } catch (error) {
      setSnackBar({
        open: true,
        message: error.message || 'Failed to load job details',
        severity: 'error'
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Add function to export Google Sheets links
  const handleExportLinks = async (jobId, includeDebug = false) => {
    setActionLoading(`links-${jobId}`);
    
    try {
      // Always make API call to generate fresh Google Sheets links
      const result = await exportLeadLinks(jobId, includeDebug);
      
      if (result.success) {
        // Open the result dialog with the data
        setExportLinksDialog({
          open: true,
          data: result.data,
          jobId: jobId,
          includeDebug: includeDebug
        });
        
        // Also show a brief success notification
        setSnackBar({
          isOpen: true,
          message: result.message || 'Google Sheets links generated successfully',
          severity: 'success',
        });
      } else {
        setSnackBar({
          isOpen: true,
          message: result.message || 'Failed to generate Google Sheets links',
          severity: 'error',
        });
      }
    } catch (error) {
      console.error('Error exporting Google Sheets links:', error);
      setSnackBar({
        isOpen: true,
        message: 'Failed to export Google Sheets links: ' + error.message,
        severity: 'error',
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleCloseExportLinksDialog = () => {
    setExportLinksDialog({
      open: false,
      data: null,
      jobId: null,
      includeDebug: false
    });
  };

  // Add pagination handlers
  const handlePageChange = (newPage) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1); // Reset to first page when changing rows per page
  };

  return (
    <ThemeProvider theme={theme}>
      <JeffAdminPanel title="Lead Management" activeTab="leads">
        <Container maxWidth="xl">
          {/* Main content */}
          <Paper 
            sx={{ 
              mt: 3, 
              p: 0, 
              backgroundColor: isDarkMode ? '#2D3748' : '#FFFFFF',
              boxShadow: '0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24)',
              borderRadius: '8px',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange} 
                aria-label="lead management tabs"
                sx={{
                  backgroundColor: isDarkMode ? '#1A202C' : '#F7FAFC',
                  '& .MuiTab-root': {
                    color: isDarkMode ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.6)',
                    fontWeight: 500,
                    '&.Mui-selected': {
                      color: isDarkMode ? '#90CAF9' : '#1976d2',
                    }
                  },
                  '& .MuiTabs-indicator': {
                    backgroundColor: isDarkMode ? '#90CAF9' : '#1976d2',
                  }
                }}
              >
                <Tab label="Jobs" id="tab-0" aria-controls="tabpanel-0" />
                <Tab label="Lead Scraper" id="tab-1" aria-controls="tabpanel-1" />
                <Tab label="Lead Modifier" id="tab-2" aria-controls="tabpanel-2" />
              </Tabs>
            </Box>

            {/* Jobs Tab */}
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ p: 3 }}>
                {/* Existing Jobs UI */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <TextField
                    placeholder="Search jobs..."
                    variant="outlined"
                    size="small"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    sx={{ 
                      width: '300px',
                      backgroundColor: isDarkMode ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
                    }}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <SearchIcon sx={{ color: isDarkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(0, 0, 0, 0.5)' }} />
                        </InputAdornment>
                      ),
                    }}
                  />
                  <Box>
                    <Button
                      startIcon={<RefreshIcon />}
                      variant="outlined"
                      onClick={loadJobs}
                      sx={{ mr: 2 }}
                    >
                      Refresh
                    </Button>
                    <Button
                      startIcon={<BusinessIcon />}
                      variant="contained"
                      onClick={handleBulkExport}
                      disabled={selectedJobs.length === 0}
                    >
                      Export Selected
                    </Button>
                  </Box>
                </Box>

                <JobsTable 
                  jobs={jobs}
                  isLoading={isJobsLoading}
                  actionLoading={actionLoading}
                  onDeleteJob={openDeleteConfirmation}
                  onExportJob={handleExportJob}
                  onExportInputData={handleExportInputData}
                  onExportLinks={handleExportLinks}
                  onCheckStatus={handleCheckJobStatus}
                  onViewJobDetails={handleViewJobDetails}
                  searchTerm={searchTerm}
                  selectedJobs={selectedJobs}
                  onJobSelection={handleJobSelection}
                  onSelectAllJobs={handleSelectAllJobs}
                  isDarkMode={isDarkMode}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleRowsPerPageChange}
                />
              </Box>
            </TabPanel>

            {/* Lead Scraper Tab */}
            <TabPanel value={tabValue} index={1}>
              <LeadScraperTabs
                singleLeadForm={singleLeadForm}
                onSingleLeadInputChange={handleLocalSingleLeadInputChange}
                onSingleLeadSubmit={handleSingleLeadSubmit}
                isSingleLeadSubmitting={actionLoading === 'singleLead'}
                currentUrl={currentUrl}
                setCurrentUrl={setCurrentUrl}
                onAddUrl={handleLocalAddUrl}
                onRemoveUrl={handleLocalRemoveUrl}
                onUrlKeyPress={handleLocalUrlKeyPress}
                csvFile={csvFile}
                setCsvFile={setCsvFile}
                batchMode={batchMode}
                setBatchMode={setBatchMode}
                batchUseDomain={batchUseDomain}
                setBatchUseDomain={setBatchUseDomain}
                onBatchLeadUpload={handleBatchLeadUpload}
                isBatchUploading={actionLoading === 'batchUpload'}
              />
            </TabPanel>

            {/* Lead Modifier Tab */}
            <TabPanel value={tabValue} index={2}>
              <LeadModifierTabs
                uploadType={uploadType}
                setUploadType={setUploadType}
                uploadOperation={uploadOperation}
                setUploadOperation={setUploadOperation}
                uploadFile={uploadFile}
                setUploadFile={setUploadFile}
                skipErrorRows={skipErrorRows}
                setSkipErrorRows={setSkipErrorRows}
                allowBlankRows={allowBlankRows}
                setAllowBlankRows={setAllowBlankRows}
                onFileChange={handleFileChange}
                onFileUpload={handleFileUpload}
                isUploading={actionLoading === 'fileUpload'}
              />
            </TabPanel>
          </Paper>

          {/* Keep the dialogs for backward compatibility */}
          <SingleLeadForm
            open={openSingleLeadDialog}
            onClose={handleSingleLeadDialogClose}
            form={singleLeadForm}
            onInputChange={handleLocalSingleLeadInputChange}
            onSubmit={handleSingleLeadSubmit}
            isSubmitting={actionLoading === 'singleLead'}
            currentUrl={currentUrl}
            setCurrentUrl={setCurrentUrl}
            onAddUrl={handleLocalAddUrl}
            onRemoveUrl={handleLocalRemoveUrl}
            onUrlKeyPress={handleLocalUrlKeyPress}
          />

          <BatchUploadForm
            open={openUploadDialog}
            onClose={handleCloseDialog}
            uploadType={uploadType}
            setUploadType={setUploadType}
            uploadFile={uploadFile}
            setUploadFile={setUploadFile}
            skipErrorRows={skipErrorRows}
            setSkipErrorRows={setSkipErrorRows}
            uploadOperation={uploadOperation}
            setUploadOperation={setUploadOperation}
            onFileChange={handleFileChange}
            onUpload={handleFileUpload}
            isUploading={actionLoading === 'fileUpload'}
          />

          <DeleteConfirmationDialog
            open={deleteConfirmDialog.open}
            onClose={closeDeleteConfirmation}
            onConfirm={confirmAndDeleteJob}
            isDeleting={actionLoading === deleteConfirmDialog.jobId}
          />

          <ExportLinksResultDialog
            open={exportLinksDialog.open}
            onClose={handleCloseExportLinksDialog}
            data={exportLinksDialog.data}
            jobId={exportLinksDialog.jobId}
            includeDebug={exportLinksDialog.includeDebug}
            isDarkMode={isDarkMode}
          />

          <Snackbar
            open={snackBar.isOpen}
            autoHideDuration={6000}
            onClose={() => setSnackBar({ ...snackBar, isOpen: false })}
          >
            <Alert 
              onClose={() => setSnackBar({ ...snackBar, isOpen: false })} 
              severity={snackBar.severity}
              variant="filled"
              sx={{ width: '100%' }}
            >
              {snackBar.message}
            </Alert>
          </Snackbar>
        </Container>
      </JeffAdminPanel>
    </ThemeProvider>
  );
};

export default LeadManagementPage;
