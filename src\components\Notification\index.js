import React from 'react';
import { Snackbar, Alert, IconButton } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const Notification = ({
  open,
  message,
  severity,
  onClose,
  duration = 6000,
  autoHide = true,
}) => {
  // If message is a string, convert newlines to <br />
  let renderedMessage = message;
  if (typeof message === 'string' && message.includes('\n')) {
    renderedMessage = message.split('\n').map((line, idx) => (
      <React.Fragment key={idx}>
        {line}
        <br />
      </React.Fragment>
    ));
  }
  return (
    <Snackbar
      open={open}
      {...(autoHide ? { autoHideDuration: duration } : {})}
      onClose={onClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
    >
      <Alert
        onClose={onClose}
        severity={severity}
        variant="filled"
        sx={{ width: '100%' }}
        action={
          <IconButton
            size="small"
            aria-label="close"
            color="inherit"
            onClick={onClose}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      >
        {renderedMessage}
      </Alert>
    </Snackbar>
  );
};

export default Notification;
