'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Typography,
  Collapse,
  Chip,
  Alert,
  CircularProgress,
  Tooltip,
  IconButton,
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { useAdminTheme } from 'views/Jeff/AdminThemeContext';
import { fetchSearchPatterns, fetchSearchColumns } from '../utils/api';

const SearchPatternSelector = ({
  selectedPattern,
  onPatternChange,
  customPattern,
  onCustomPatternChange,
  error,
  disabled = false,
}) => {
  // Validate custom pattern
  const validateCustomPattern = (pattern) => {
    if (!pattern || !pattern.trim()) {
      return 'Custom pattern is required';
    }

    if (!pattern.includes('{{') || !pattern.includes('}}')) {
      return 'Pattern must include at least one {{columnName}} placeholder';
    }

    // Check for valid column name syntax
    const columnMatches = pattern.match(/\{\{([^}]+)\}\}/g);
    if (columnMatches) {
      for (const match of columnMatches) {
        const columnName = match.slice(2, -2).trim();
        if (!columnName || columnName.includes(' ')) {
          return 'Column names in {{}} must not contain spaces';
        }
      }
    }

    return null;
  };

  const customPatternError =
    selectedPattern === 'custom' ? validateCustomPattern(customPattern) : null;
  const { mode: adminThemeMode } = useAdminTheme() || { mode: 'light' };
  const isDarkMode = adminThemeMode === 'dark';

  const [patterns, setPatterns] = useState([]);
  const [columns, setColumns] = useState([]);
  const [loading, setLoading] = useState(true);
  const [apiError, setApiError] = useState(null);

  // Default patterns if API fails
  const defaultPatterns = [
    {
      value: 'original',
      label: 'Original (sellerName businessName)',
      description: 'Default search pattern',
    },
    {
      value: 'quoted_with_shop',
      label: 'Quoted with Shop ("sellerName" | "businessName" shop)',
      description: 'Quoted names with shop keyword',
    },
    {
      value: 'unquoted_with_shop',
      label: 'Unquoted with Shop (sellerName businessName shop)',
      description: 'Unquoted names with shop keyword',
    },
    {
      value: 'custom',
      label: 'Custom Pattern',
      description: 'Define your own search pattern',
    },
  ];

  const defaultColumns = [
    'sellerName',
    'businessName',
    'address',
    'country',
    'sellerUrl',
    'metadata',
  ];

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      setApiError(null);

      try {
        const [patternsResult, columnsResult] = await Promise.all([
          fetchSearchPatterns(),
          fetchSearchColumns(),
        ]);

        if (patternsResult.success) {
          setPatterns(patternsResult.patterns);
        } else {
          console.warn(
            'Failed to fetch patterns, using defaults:',
            patternsResult.message,
          );
          setPatterns(defaultPatterns);
        }

        if (columnsResult.success) {
          setColumns(columnsResult.columns);
        } else {
          console.warn(
            'Failed to fetch columns, using defaults:',
            columnsResult.message,
          );
          setColumns(defaultColumns);
        }
      } catch (error) {
        console.error('Error loading search pattern data:', error);
        setApiError('Failed to load search pattern options');
        setPatterns(defaultPatterns);
        setColumns(defaultColumns);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  const handlePatternChange = (event) => {
    const value = event.target.value;
    onPatternChange(value);

    // Clear custom pattern if switching away from custom
    if (value !== 'custom' && onCustomPatternChange) {
      onCustomPatternChange('');
    }
  };

  const examplePatterns = [
    '{{sellerName}} {{businessName}} shop',
    '"{{sellerName}}" | "{{businessName}}" {{country}}',
    '{{sellerName}} in {{country}}',
    '{{businessName}} {{address}}',
  ];

  if (loading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, p: 2 }}>
        <CircularProgress size={20} />
        <Typography variant="body2">Loading search patterns...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ mt: 2 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
        <FormLabel component="legend" sx={{ fontWeight: 600 }}>
          Search Pattern
        </FormLabel>
        <Tooltip title="Choose how to format the search query for lead generation">
          <IconButton size="small">
            <HelpOutlineIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      {apiError && (
        <Alert severity="warning" sx={{ mb: 2 }}>
          {apiError}. Using default options.
        </Alert>
      )}

      <FormControl component="fieldset" disabled={disabled} fullWidth>
        <RadioGroup
          value={selectedPattern || 'original'}
          onChange={handlePatternChange}
        >
          {patterns.map((pattern) => (
            <FormControlLabel
              key={pattern.value}
              value={pattern.value}
              control={<Radio />}
              label={
                <Box>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {pattern.label}
                  </Typography>
                  {pattern.description && (
                    <Typography variant="caption" color="text.secondary">
                      {pattern.description}
                    </Typography>
                  )}
                </Box>
              }
              sx={{ mb: 1 }}
            />
          ))}
        </RadioGroup>
      </FormControl>

      <Collapse in={selectedPattern === 'custom'}>
        <Box
          sx={{
            mt: 2,
            p: 2,
            backgroundColor: isDarkMode
              ? 'rgba(255,255,255,0.02)'
              : 'rgba(0,0,0,0.02)',
            borderRadius: 1,
          }}
        >
          <TextField
            fullWidth
            label="Custom Search Pattern"
            value={customPattern || ''}
            onChange={(e) =>
              onCustomPatternChange && onCustomPatternChange(e.target.value)
            }
            placeholder="Enter custom pattern using {{columnName}} syntax"
            variant="outlined"
            size="small"
            disabled={disabled}
            error={!!(error || customPatternError)}
            helperText={
              error ||
              customPatternError ||
              'Use {{columnName}} syntax to reference data fields'
            }
            sx={{ mb: 2 }}
          />

          <Typography variant="subtitle2" gutterBottom>
            Available Columns:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2 }}>
            {columns.map((column) => (
              <Chip
                key={column}
                label={`{{${column}}}`}
                size="small"
                variant="outlined"
                onClick={() => {
                  if (onCustomPatternChange && !disabled) {
                    const newPattern = (customPattern || '') + `{{${column}}} `;
                    onCustomPatternChange(newPattern);
                  }
                }}
                sx={{ cursor: disabled ? 'default' : 'pointer' }}
              />
            ))}
          </Box>

          <Typography variant="subtitle2" gutterBottom>
            Example Patterns:
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            {examplePatterns.map((example, index) => (
              <Typography
                key={index}
                variant="caption"
                sx={{
                  fontFamily: 'monospace',
                  backgroundColor: isDarkMode
                    ? 'rgba(255,255,255,0.05)'
                    : 'rgba(0,0,0,0.05)',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  cursor: disabled ? 'default' : 'pointer',
                  '&:hover': disabled
                    ? {}
                    : {
                        backgroundColor: isDarkMode
                          ? 'rgba(255,255,255,0.1)'
                          : 'rgba(0,0,0,0.1)',
                      },
                }}
                onClick={() => {
                  if (onCustomPatternChange && !disabled) {
                    onCustomPatternChange(example);
                  }
                }}
              >
                {example}
              </Typography>
            ))}
          </Box>
        </Box>
      </Collapse>
    </Box>
  );
};

export default SearchPatternSelector;
